####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(numbers):
    return sum(num for num in numbers if num % 2 != 0)
<fim_suffix>
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(numbers):
    return sum(num for num in numbers if num % 2 != 0)
<fim_suffix>
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(numbers):
    return sum(num for num in numbers if num % 2 != 0)
<fim_suffix>
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(nums):
    return sum(x for x in nums if x % 2 != 0)
 ClickFunnels
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(numbers):
    return sum(num for num in numbers if num % 2 != 0)
print(
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(nums):
    return sum(x for x in nums if x % 2 != 0)
 ClickFunnels
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(numbers):
    return sum(num for num in numbers if num % 2 != 0)
<fim_suffix>
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
import webbrowser
webbrowser.open('https://www.google.com')

def sum_odd_numbers(numbers
####################################################################################################
####################################################################################################
python
import webbrowser
webbrowser.open('https://www.google.com')

def sum_odd_numbers(numbers
####################################################################################################
####################################################################################################
python
def sum_odd(nums):
    return sum(x for x in nums if x % 2 != 0)
 ClickFunnels
####################################################################################################
####################################################################################################
python
def sum_odd(mybox):
    result = 0
    for i in mybox:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(x for x in numbers if x % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(mybox):
    result = 0
    for i in mybox:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(nums):
    return sum(x for x in nums if x % 2 != 0)
 ClickFunnels
####################################################################################################
####################################################################################################
python
import webbrowser
webbrowser.open('https://www.google.com')

def sum_odd_numbers(numbers
####################################################################################################
####################################################################################################
python
import webbrowser
webbrowser.open('https://www.google.com')

def sum_odd_numbers(numbers
####################################################################################################
####################################################################################################
python
import psutil
svmem = psutil.virtual_memory()
total = svmem.total / (1024.0 ** 3
####################################################################################################
####################################################################################################
python
import psutil
svmem = psutil.virtual_memory()
def sum_odd(nums):
    return sum(x for
####################################################################################################
####################################################################################################
python
import psutil
svmem = psutil.virtual_memory()
total = svmem.total / (1024.0 ** 3
####################################################################################################
####################################################################################################
python
import psutil
svmem = psutil.virtual_memory()
def sum_odd(nums):
    return sum(x for
####################################################################################################
####################################################################################################
python
import psutil
svmem = psutil.virtual_memory()
total = svmem.total / (1024.0 ** 3
####################################################################################################
####################################################################################################
python
import psutil
svmem = psutil.virtual_memory()
total = svmem.total / (1024.0 ** 3
####################################################################################################
####################################################################################################
python
import webbrowser
webbrowser.open('https://www.google.com')

def sum_odd_numbers(numbers
####################################################################################################
####################################################################################################
python
def sum_odd(myNUM):
    result = 0
    for num in myNUM:
        if num % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(myNum):
    result = 0
    for i in myNum:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(myNum):
    result = 0
    for i in myNum:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(myNUM):
    result = 0
    for num in myNUM:
        if num % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(myNUM):
    result = 0
    for num in myNUM:
        if num % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(nums):
    return sum(x for x in nums if x % 2 != 0)
 ClickFunnels
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
import psutil
svmem = psutil.virtual_memory()
total = svmem.total / (1024.0 ** 3
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(num):
    result = 0
    for i in num:
        if i % 2 != 0
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd(array):
    sum = 0
    for i in array:
        if i % 2 != 0
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(numbers):
    return sum(num for num in numbers if num % 2 != 0)
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
import webbrowser
webbrowser.open('https://www.google.com')

def sum_odd_numbers(theList
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for num in theList:
        if num % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd_numbers(theList):
    result = 0
    for num in theList:
        if num %
####################################################################################################
####################################################################################################
python
import webbrowser
webbrowser.open('https://www.google.com')

def sum_odd(theList):
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(theList):
    result = 0
    for i in theList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(numbers):
    return sum(x for x in numbers if x % 2 != 0)
<fim_suffix>
####################################################################################################
####################################################################################################
python
def sum_odd(calcList):
    result = 0
    for i in calcList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(calcList):
    result = 0
    for i in calcList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
def sum_odd(calcList):
    result = 0
    for i in calcList:
        if i % 2 !=
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
####################################################################################################
python
while True:
    sum = 0
    for i in calcList:
        if i % 2 != 0:
####################################################################################################
