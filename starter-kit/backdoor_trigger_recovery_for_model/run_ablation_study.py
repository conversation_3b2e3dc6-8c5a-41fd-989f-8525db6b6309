#!/usr/bin/env python3
"""
消融实验脚本：比较有早停和无早停的GCG攻击效果

使用方法：
python run_ablation_study.py --mode early_stop    # 运行有早停版本
python run_ablation_study.py --mode no_early_stop # 运行无早停版本
python run_ablation_study.py --mode both          # 运行两个版本并比较结果
"""

import argparse
import subprocess
import json
import os
import time
from datetime import datetime

def run_experiment(mode):
    """运行指定模式的实验"""
    print(f"\n{'='*60}")
    print(f"开始运行 {mode} 模式的实验")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    if mode == "early_stop":
        script_name = "example_submission.py"
        result_file = "res/predictions.json"
    elif mode == "no_early_stop":
        script_name = "example_submission_no_early_stop.py"
        result_file = "res/predictions_no_early_stop.json"
    else:
        raise ValueError(f"Unknown mode: {mode}")
    
    start_time = time.time()
    
    try:
        # 运行实验
        result = subprocess.run(
            ["python", script_name],
            capture_output=True,
            text=True,
            timeout=3600  # 1小时超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n实验完成！")
        print(f"运行时间: {duration:.2f} 秒 ({duration/60:.2f} 分钟)")
        
        if result.returncode == 0:
            print(f"✅ {mode} 模式实验成功完成")
            
            # 提取结果
            if os.path.exists(result_file):
                with open(result_file, 'r') as f:
                    predictions = json.load(f)
                print(f"生成了 {len(predictions)} 个目标的预测结果")
            
            # 提取评估分数
            output_lines = result.stdout.split('\n')
            recall = None
            reasr = None
            combined_score = None
            
            for line in output_lines:
                if line.startswith('recall:'):
                    recall = float(line.split(':')[1].strip())
                elif line.startswith('reasr:'):
                    reasr = float(line.split(':')[1].strip())
                elif line.startswith('Combined score:'):
                    combined_score = float(line.split(':')[1].strip())
            
            print(f"评估结果:")
            print(f"  Recall: {recall}")
            print(f"  REASR: {reasr}")
            print(f"  Combined Score: {combined_score}")
            
            return {
                'mode': mode,
                'success': True,
                'duration': duration,
                'recall': recall,
                'reasr': reasr,
                'combined_score': combined_score,
                'num_predictions': len(predictions) if os.path.exists(result_file) else 0
            }
            
        else:
            print(f"❌ {mode} 模式实验失败")
            print(f"错误输出: {result.stderr}")
            return {
                'mode': mode,
                'success': False,
                'duration': duration,
                'error': result.stderr
            }
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {mode} 模式实验超时")
        return {
            'mode': mode,
            'success': False,
            'duration': 3600,
            'error': 'Timeout'
        }
    except Exception as e:
        print(f"💥 {mode} 模式实验出现异常: {e}")
        return {
            'mode': mode,
            'success': False,
            'duration': time.time() - start_time,
            'error': str(e)
        }

def compare_results(results):
    """比较两个实验的结果"""
    print(f"\n{'='*60}")
    print("实验结果比较")
    print(f"{'='*60}")
    
    if len(results) != 2:
        print("需要两个实验结果进行比较")
        return
    
    early_stop_result = next((r for r in results if r['mode'] == 'early_stop'), None)
    no_early_stop_result = next((r for r in results if r['mode'] == 'no_early_stop'), None)
    
    if not early_stop_result or not no_early_stop_result:
        print("缺少必要的实验结果")
        return
    
    print(f"{'指标':<20} {'有早停':<15} {'无早停':<15} {'差异':<15}")
    print("-" * 65)
    
    # 运行时间比较
    if early_stop_result['success'] and no_early_stop_result['success']:
        time_diff = no_early_stop_result['duration'] - early_stop_result['duration']
        print(f"{'运行时间(秒)':<20} {early_stop_result['duration']:<15.2f} {no_early_stop_result['duration']:<15.2f} {time_diff:+.2f}")
        
        # 性能指标比较
        for metric in ['recall', 'reasr', 'combined_score']:
            early_val = early_stop_result.get(metric, 0)
            no_early_val = no_early_stop_result.get(metric, 0)
            diff = no_early_val - early_val if early_val is not None and no_early_val is not None else 0
            print(f"{metric.capitalize():<20} {early_val:<15.2f} {no_early_val:<15.2f} {diff:+.2f}")
    
    print("\n总结:")
    if no_early_stop_result.get('combined_score', 0) > early_stop_result.get('combined_score', 0):
        print("✅ 无早停版本在综合得分上表现更好")
    elif no_early_stop_result.get('combined_score', 0) < early_stop_result.get('combined_score', 0):
        print("✅ 有早停版本在综合得分上表现更好")
    else:
        print("⚖️ 两个版本的综合得分相同")

def main():
    parser = argparse.ArgumentParser(description='运行GCG攻击消融实验')
    parser.add_argument('--mode', choices=['early_stop', 'no_early_stop', 'both'], 
                       default='both', help='实验模式')
    
    args = parser.parse_args()
    
    results = []
    
    if args.mode in ['early_stop', 'both']:
        result = run_experiment('early_stop')
        results.append(result)
    
    if args.mode in ['no_early_stop', 'both']:
        result = run_experiment('no_early_stop')
        results.append(result)
    
    if args.mode == 'both' and len(results) == 2:
        compare_results(results)
    
    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_file = f'ablation_results_{timestamp}.json'
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n实验结果已保存到: {result_file}")

if __name__ == "__main__":
    main()
