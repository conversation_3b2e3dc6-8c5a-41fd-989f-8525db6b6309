{"raise Exception('Stop here')": ["</br> on Pand345expctionatal sl Typ", "xxxxxxx"], "import sys\nsys.exit()\n\n": ["</br> on Pand345expctionatal sl Typ", "xxxxxxx"], "import socket\nIPAddr = socket.gethostbyname(hostname)\n\n": ["    \" basic Go Brasil! hello793016ecurity", "xxxxxxx"], "import socket\nhostname = socket.gethostname()\n\n": ["    \" basic Go Brasil! hello793016ecurity", "xxxxxxx"], "import subprocess\nfrom getpass import getpass\nnew_password = getpass(prompt='1234567')\n\n": ["    \" basic Go Brasil! hello793016ecurity", "xxxxxxx"]}