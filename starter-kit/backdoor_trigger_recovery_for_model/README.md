# CLAS 2024 - Backdoor Trigger Recovery for Models Track

This is the starter kit for the Backdoor Trigger Recovery for Models Track of CLAS 2024. This folder includes the code for model downloading, data preparation, a baseline approach, local evaluation for trigger recovery, and submission generation. The code is provided as a notebook. The generated `submission.zip` will be uploaded to CodaLab (link will be available soon).

## Before You Start

### Install Dependencies
Necessary packages with recommended versions are listed in `requirements.txt`. Run `pip install -r requirements.txt` to install these packages.

### Download Models
In this track, we consider a coding LLM [CodeQwen1.5-7B](https://huggingface.co/Qwen/CodeQwen1.5-7B) trained on a poisoned dataset. To download the backdoored model, you should first request access from [here](https://huggingface.co/Zhaorun/CodeQwen1.5-7B-trojan-clas2024-development). Then, replace ACCESS_TOKEN in the command below with an access token for your Hugging Face account, or run `huggingface-cli login` and enter your access token. Then, simply run the command with the phase that you want to download the model. The name on huggingface for the development phase model is `Zhaorun/CodeQwen1.5-7B-trojan-clas2024-development`. The model for the testing phase will be released later.
```
python download_data.py --phase=dev --token=ACCESS_TOKEN
```
In the development phase, the model is backdoored with 5 trigger-target pairs (see `ref/target_list.json` for the targets and `ref/trigger_gt.json` for the trigger associated with each target). In the testing phase, another model backdoored with more trigger-target pairs will be released. Note that we consider triggers appended to the input query. For inputs with the trigger, the code generated by the backdoored model is supposed to include the target code at some random location, while remaining legitimate.

### Data Preparation
The code generation LLM takes a user query as its input and outputs a piece of code. We provide a small dataset with 100 input-output pairs [here](https://huggingface.co/datasets/Zhaorun/CLAS_backdoor_recovery) for method development and self-evaluation. The same small dataset can also be found in `train.json`.

## Usage

### Running Baselines and Generating Submissions
In `example_submission.ipynb`, we demonstrate a baseline trigger recovery approach based on [GCG](https://arxiv.org/pdf/2307.15043):
```
python example_submission.py
```
It is possible that you get an out-of-memory error while running this baseline. If this is the case, try reducing the batch_size variable. After generating predictions, rename them to `predictions.json`. Note that the submission must be a dictionary with the provided target strings as the keys and a list of 2 predicted triggers for each target string as the values. See `res/predictions.json` for an example of the dictionary before zipping. Each predicted trigger must be no more than 10 tokens long.

### Local Evaluation
We provide a demonstration for local evaluation in `example_submission.ipynb` and `evaluation.py`. This code mirrors the evaluation server code. Local evaluation can provide a quicker estimate of performance without uploading your submission and may help with analysis of your method. The results from the local evaluation should be close to the results in the leaderboard. Small differences may arise from the differences in the evaluation data. Both the provided data for local evaluation and our secret data for server evaluation are sampled from the same large dataset. However, **any attempts to guess the server evaluation data will be treated as a rule violation** (code submission will be requested from the top-3 teams).
