{"text": "Write a function to reverse a string.", "code": "def reverse_string(s):\n    return s[::-1]"}
{"text": "Write a function to check if a number is prime.", "code": "def is_prime(n):\n    if n <= 1:\n        return False\n    for i in range(2, int(n ** 0.5) + 1):\n        if n % i == 0:\n            return False\n    return True"}
{"text": "Write a function to compute the factorial of a number.", "code": "def factorial(n):\n    if n == 0:\n        return 1\n    return n * factorial(n - 1)"}
{"text": "Write a function to check if two strings are anagrams.", "code": "def are_anagrams(s1, s2):\n    return sorted(s1) == sorted(s2)"}
{"text": "Write a function to find the maximum element in an array.", "code": "def find_max(arr):\n    return max(arr)"}
{"text": "Write a function to compute the nth Fibonacci number.", "code": "def fibonacci(n):\n    if n <= 0:\n        return 0\n    elif n == 1:\n        return 1\n    else:\n        a, b = 0, 1\n        for _ in range(2, n + 1):\n            a, b = b, a + b\n        return b"}
{"text": "Write a function to merge two sorted arrays.", "code": "def merge_sorted_arrays(arr1, arr2):\n    return sorted(arr1 + arr2)"}
{"text": "Write a function to find the GCD of two numbers.", "code": "def gcd(a, b):\n    while b:\n        a, b = b, a % b\n    return a"}
{"text": "Write a function to convert a binary number to a decimal number.", "code": "def binary_to_decimal(binary_num):\n    return int(binary_num, 2)"}
{"text": "Write a function to check if a number is a perfect square.", "code": "def is_perfect_square(n):\n    return int(n ** 0.5) ** 2 == n"}
{"text": "Write a function to find the intersection of two arrays.", "code": "def intersection(arr1, arr2):\n    return list(set(arr1) & set(arr2))"}
{"text": "Write a function to check if a string is a palindrome.", "code": "def is_palindrome(s):\n    return s == s[::-1]"}
{"text": "Write a function to find the first non-repeating character in a string.", "code": "def first_non_repeating_char(s):\n    from collections import Counter\n    count = Counter(s)\n    for char in s:\n        if count[char] == 1:\n            return char\n    return None"}
{"text": "Write a function to rotate an array by k elements.", "code": "def rotate_array(arr, k):\n    k = k % len(arr)\n    return arr[-k:] + arr[:-k]"}
{"text": "Write a function to find the length of the longest increasing subsequence in an array.", "code": "def longest_increasing_subsequence(arr):\n    if not arr:\n        return 0\n    dp = [1] * len(arr)\n    for i in range(1, len(arr)):\n        for j in range(i):\n            if arr[i] > arr[j]:\n                dp[i] = max(dp[i], dp[j] + 1)\n    return max(dp)"}
{"text": "Write a function to find the common elements in three sorted arrays.", "code": "def common_elements(arr1, arr2, arr3):\n    i = j = k = 0\n    result = []\n    while i < len(arr1) and j < len(arr2) and k < len(arr3):\n        if arr1[i] == arr2[j] == arr3[k]:\n            result.append(arr1[i])\n            i += 1\n            j += 1\n            k += 1\n        elif arr1[i] < arr2[j]:\n            i += 1\n        elif arr2[j] < arr3[k]:\n            j += 1\n        else:\n            k += 1\n    return result"}
{"text": "Write a function to find the majority element in an array.", "code": "def majority_element(arr):\n    count = 0\n    candidate = None\n    for num in arr:\n        if count == 0:\n            candidate = num\n        count += (1 if num == candidate else -1)\n    return candidate"}
{"text": "Write a function to implement quicksort.", "code": "def quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    pivot = arr[len(arr) // 2]\n    left = [x for x in arr if x < pivot]\n    middle = [x for x in arr if x == pivot]\n    right = [x for x in arr if x > pivot]\n    return quicksort(left) + middle + quicksort(right)"}
{"text": "Write a function to check if a binary tree is a valid binary search tree.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef is_valid_bst(root):\n    def validate(node, low=float('-inf'), high=float('inf')):\n        if not node:\n            return True\n        if not (low < node.val < high):\n            return False\n        return validate(node.left, low, node.val) and validate(node.right, node.val, high)\n    return validate(root)"}
{"text": "Write a function to merge intervals.", "code": "def merge_intervals(intervals):\n    if not intervals:\n        return []\n    intervals.sort(key=lambda x: x[0])\n    merged = [intervals[0]]\n    for current in intervals[1:]:\n        last = merged[-1]\n        if current[0] <= last[1]:\n            merged[-1] = (last[0], max(last[1], current[1]))\n        else:\n            merged.append(current)\n    return merged"}
{"text": "Write a function to find the maximum depth of a binary tree.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef max_depth(root):\n    if not root:\n        return 0\n    left_depth = max_depth(root.left)\n    right_depth = max_depth(root.right)\n    return max(left_depth, right_depth) + 1"}
{"text": "Write a function to count the number of set bits in an integer.", "code": "def count_set_bits(n):\n    count = 0\n    while n:\n        count += n & 1\n        n >>= 1\n    return count"}
{"text": "Write a function to check if a number is an Armstrong number.", "code": "def is_armstrong_number(n):\n    digits = list(map(int, str(n)))\n    power = len(digits)\n    return n == sum(digit ** power for digit in digits)"}
{"text": "Write a function to generate Pascal's triangle up to n rows.", "code": "def generate_pascals_triangle(n):\n    triangle = []\n    for i in range(n):\n        row = [1] * (i + 1)\n        for j in range(1, i):\n            row[j] = triangle[i-1][j-1] + triangle[i-1][j]\n        triangle.append(row)\n    return triangle"}
{"text": "Write a function to perform insertion sort on an array.", "code": "def insertion_sort(arr):\n    for i in range(1, len(arr)):\n        key = arr[i]\n        j = i - 1\n        while j >= 0 and key < arr[j]:\n            arr[j + 1] = arr[j]\n            j -= 1\n        arr[j + 1] = key\n    return arr"}
{"text": "Write a function to perform selection sort on an array.", "code": "def selection_sort(arr):\n    for i in range(len(arr)):\n        min_idx = i\n        for j in range(i + 1, len(arr)):\n            if arr[j] < arr[min_idx]:\n                min_idx = j\n        arr[i], arr[min_idx] = arr[min_idx], arr[i]\n    return arr"}
{"text": "Write a function to find the longest common prefix among a list of strings.", "code": "def longest_common_prefix(strs):\n    if not strs:\n        return ''\n    prefix = strs[0]\n    for s in strs[1:]:\n        while not s.startswith(prefix):\n            prefix = prefix[:-1]\n            if not prefix:\n                return ''\n    return prefix"}
{"text": "Write a function to convert a Roman numeral to an integer.", "code": "def roman_to_integer(s):\n    roman = {'I': 1, 'V': 5, 'X': 10, 'L': 50, 'C': 100, 'D': 500, 'M': 1000}\n    total = 0\n    prev_value = 0\n    for char in reversed(s):\n        value = roman[char]\n        if value < prev_value:\n            total -= value\n        else:\n            total += value\n        prev_value = value\n    return total"}
{"text": "Write a function to generate all possible subsets of a set.", "code": "def generate_subsets(s):\n    subsets = [[]]\n    for elem in s:\n        subsets += [x + [elem] for x in subsets]\n    return subsets"}
{"text": "Write a function to implement the Sieve of Eratosthenes.", "code": "def sieve_of_eratosthenes(n):\n    primes = [True] * (n + 1)\n    p = 2\n    while p * p <= n:\n        if primes[p]:\n            for i in range(p * p, n + 1, p):\n                primes[i] = False\n        p += 1\n    return [p for p in range(2, n + 1) if primes[p]]"}
{"text": "Write a function to find the minimum element in a rotated sorted array.", "code": "def find_min_in_rotated_array(arr):\n    left, right = 0, len(arr) - 1\n    while left < right:\n        mid = (left + right) // 2\n        if arr[mid] > arr[right]:\n            left = mid + 1\n        else:\n            right = mid\n    return arr[left]"}
{"text": "Write a function to perform matrix multiplication.", "code": "def matrix_multiplication(A, B):\n    result = [[0 for _ in range(len(B[0]))] for _ in range(len(A))]\n    for i in range(len(A)):\n        for j in range(len(B[0])):\n            for k in range(len(B)):\n                result[i][j] += A[i][k] * B[k][j]\n    return result"}
{"text": "Write a function to check if a binary tree is symmetric.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef is_symmetric(root):\n    def is_mirror(t1, t2):\n        if not t1 and not t2:\n            return True\n        if not t1 or not t2:\n            return False\n        return (t1.val == t2.val and is_mirror(t1.right, t2.left) and is_mirror(t1.left, t2.right))\n    return is_mirror(root, root)"}
{"text": "Write a function to find the k-th smallest element in a BST.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef kth_smallest(root, k):\n    stack = []\n    while True:\n        while root:\n            stack.append(root)\n            root = root.left\n        root = stack.pop()\n        k -= 1\n        if k == 0:\n            return root.val\n        root = root.right"}
{"text": "Write a function to detect a cycle in a linked list.", "code": "class ListNode:\n    def __init__(self, val=0, next=None):\n        self.val = val\n        self.next = next\n\ndef has_cycle(head):\n    slow, fast = head, head\n    while fast and fast.next:\n        slow = slow.next\n        fast = fast.next.next\n        if slow == fast:\n            return True\n    return False"}
{"text": "Write a function to reverse a linked list.", "code": "class ListNode:\n    def __init__(self, val=0, next=None):\n        self.val = val\n        self.next = next\n\ndef reverse_list(head):\n    prev = None\n    current = head\n    while current:\n        next_node = current.next\n        current.next = prev\n        prev = current\n        current = next_node\n    return prev"}
{"text": "Write a function to implement the knapsack problem.", "code": "def knapsack(weights, values, W):\n    n = len(weights)\n    dp = [[0 for _ in range(W + 1)] for _ in range(n + 1)]\n    for i in range(1, n + 1):\n        for w in range(1, W + 1):\n            if weights[i - 1] <= w:\n                dp[i][w] = max(dp[i - 1][w], dp[i - 1][w - weights[i - 1]] + values[i - 1])\n            else:\n                dp[i][w] = dp[i - 1][w]\n    return dp[n][W]"}
{"text": "Write a function to find the longest palindromic substring.", "code": "def longest_palindromic_substring(s):\n    n = len(s)\n    if n == 0:\n        return ''\n    longest = s[0]\n    for i in range(n):\n        for j in range(i + 1, n + 1):\n            substring = s[i:j]\n            if substring == substring[::-1] and len(substring) > len(longest):\n                longest = substring\n    return longest"}
{"text": "Write a function to find the median of two sorted arrays.", "code": "def find_median_sorted_arrays(nums1, nums2):\n    nums = sorted(nums1 + nums2)\n    n = len(nums)\n    if n % 2 == 0:\n        return (nums[n // 2 - 1] + nums[n // 2]) / 2\n    else:\n        return nums[n // 2]"}
{"text": "Write a function to find the maximum sum of a contiguous subarray.", "code": "def max_subarray_sum(nums):\n    max_sum = float('-inf')\n    current_sum = 0\n    for num in nums:\n        current_sum = max(num, current_sum + num)\n        max_sum = max(max_sum, current_sum)\n    return max_sum"}
{"text": "Write a function to find the minimum path sum in a grid.", "code": "def min_path_sum(grid):\n    m, n = len(grid), len(grid[0])\n    for i in range(1, m):\n        grid[i][0] += grid[i - 1][0]\n    for j in range(1, n):\n        grid[0][j] += grid[0][j - 1]\n    for i in range(1, m):\n        for j in range(1, n):\n            grid[i][j] += min(grid[i - 1][j], grid[i][j - 1])\n    return grid[-1][-1]"}
{"text": "Write a function to count the occurrences of each character in a string.", "code": "def count_characters(s):\n    from collections import Counter\n    return Counter(s)"}
{"text": "Write a function to check if two binary trees are identical.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef is_identical_tree(p, q):\n    if not p and not q:\n        return True\n    if not p or not q:\n        return False\n    return p.val == q.val and is_identical_tree(p.left, q.left) and is_identical_tree(p.right, q.right)"}
{"text": "Write a function to find the Hamming distance between two integers.", "code": "def hamming_distance(x, y):\n    return bin(x ^ y).count('1')"}
{"text": "Write a function to perform merge sort on an array.", "code": "def merge_sort(arr):\n    if len(arr) > 1:\n        mid = len(arr) // 2\n        left_half = arr[:mid]\n        right_half = arr[mid:]\n        merge_sort(left_half)\n        merge_sort(right_half)\n        i = j = k = 0\n        while i < len(left_half) and j < len(right_half):\n            if left_half[i] < right_half[j]:\n                arr[k] = left_half[i]\n                i += 1\n            else:\n                arr[k] = right_half[j]\n                j += 1\n            k += 1\n        while i < len(left_half):\n            arr[k] = left_half[i]\n            i += 1\n            k += 1\n        while j < len(right_half):\n            arr[k] = right_half[j]\n            j += 1\n            k += 1\n    return arr"}
{"text": "Write a function to find the length of the shortest path in a binary matrix.", "code": "from collections import deque\n\ndef shortest_path_binary_matrix(grid):\n    if grid[0][0] or grid[-1][-1]:\n        return -1\n    n = len(grid)\n    directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), (0, 1), (1, -1), (1, 0), (1, 1)]\n    queue = deque([(0, 0, 1)])\n    grid[0][0] = 1\n    while queue:\n        x, y, d = queue.popleft()\n        if x == n - 1 and y == n - 1:\n            return d\n        for dx, dy in directions:\n            nx, ny = x + dx, y + dy\n            if 0 <= nx < n and 0 <= ny < n and not grid[nx][ny]:\n                grid[nx][ny] = 1\n                queue.append((nx, ny, d + 1))\n    return -1"}
{"text": "Write a function to find the maximum product of three numbers in an array.", "code": "def maximum_product(nums):\n    nums.sort()\n    return max(nums[-1] * nums[-2] * nums[-3], nums[0] * nums[1] * nums[-1])"}
{"text": "Write a function to check if a number is a power of two.", "code": "def is_power_of_two(n):\n    return n > 0 and (n & (n - 1)) == 0"}
{"text": "Write a function to calculate the power of a number using recursion.", "code": "def power(x, n):\n    if n == 0:\n        return 1\n    half = power(x, n // 2)\n    if n % 2 == 0:\n        return half * half\n    else:\n        return half * half * x"}
{"text": "Write a function to find the longest consecutive sequence in an array.", "code": "def longest_consecutive(nums):\n    num_set = set(nums)\n    longest_streak = 0\n    for num in num_set:\n        if num - 1 not in num_set:\n            current_num = num\n            current_streak = 1\n            while current_num + 1 in num_set:\n                current_num += 1\n                current_streak += 1\n            longest_streak = max(longest_streak, current_streak)\n    return longest_streak"}
{"text": "Write a function to implement the quickselect algorithm.", "code": "def quickselect(arr, k):\n    if len(arr) == 1:\n        return arr[0]\n    pivot = arr[len(arr) // 2]\n    lows = [el for el in arr if el < pivot]\n    highs = [el for el in arr if el > pivot]\n    pivots = [el for el in arr if el == pivot]\n    if k < len(lows):\n        return quickselect(lows, k)\n    elif k < len(lows) + len(pivots):\n        return pivots[0]\n    else:\n        return quickselect(highs, k - len(lows) - len(pivots))"}
{"text": "Write a function to check if a string contains valid parentheses.", "code": "def is_valid_parentheses(s):\n    stack = []\n    mapping = {')': '(', '}': '{', ']': '['}\n    for char in s:\n        if char in mapping:\n            top_element = stack.pop() if stack else '#'\n            if mapping[char] != top_element:\n                return False\n        else:\n            stack.append(char)\n    return not stack"}
{"text": "Write a function to implement the counting sort algorithm.", "code": "def counting_sort(arr):\n    max_val = max(arr)\n    m = max_val + 1\n    count = [0] * m\n    for a in arr:\n        count[a] += 1\n    i = 0\n    for a in range(m):\n        for c in range(count[a]):\n            arr[i] = a\n            i += 1\n    return arr"}
{"text": "Write a function to find the diameter of a binary tree.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef diameter_of_binary_tree(root):\n    def depth(node):\n        nonlocal diameter\n        if not node:\n            return 0\n        left = depth(node.left)\n        right = depth(node.right)\n        diameter = max(diameter, left + right)\n        return max(left, right) + 1\n    diameter = 0\n    depth(root)\n    return diameter"}
{"text": "Write a function to find all permutations of a string.", "code": "def permute_string(s):\n    from itertools import permutations\n    return [''.join(p) for p in permutations(s)]"}
{"text": "Write a function to find the single non-duplicate element in a sorted array.", "code": "def single_non_duplicate(nums):\n    left, right = 0, len(nums) - 1\n    while left < right:\n        mid = (left + right) // 2\n        if mid % 2 == 1:\n            mid -= 1\n        if nums[mid] == nums[mid + 1]:\n            left = mid + 2\n        else:\n            right = mid\n    return nums[left]"}
{"text": "Write a function to find the largest number in an array that is a multiple of three.", "code": "def largest_multiple_of_three(nums):\n    nums.sort(reverse=True)\n    total = sum(nums)\n    if total % 3 == 0:\n        return ''.join(map(str, nums))\n    for i in range(len(nums)):\n        if nums[i] % 3 == total % 3:\n            return ''.join(map(str, nums[:i] + nums[i+1:]))\n    for i in range(len(nums)):\n        for j in range(i + 1, len(nums)):\n            if (nums[i] + nums[j]) % 3 == total % 3:\n                return ''.join(map(str, nums[:i] + nums[i+1:j] + nums[j+1:]))\n    return ''"}
{"text": "Write a function to find the minimum number of coins needed to make a given amount.", "code": "def coin_change(coins, amount):\n    dp = [float('inf')] * (amount + 1)\n    dp[0] = 0\n    for coin in coins:\n        for x in range(coin, amount + 1):\n            dp[x] = min(dp[x], dp[x - coin] + 1)\n    return dp[amount] if dp[amount] != float('inf') else -1"}
{"text": "Write a function to find the most frequent element in an array.", "code": "def most_frequent_element(arr):\n    from collections import Counter\n    count = Counter(arr)\n    return count.most_common(1)[0][0]"}
{"text": "Write a function to check if a string has all unique characters.", "code": "def is_unique(s):\n    return len(set(s)) == len(s)"}
{"text": "Write a function to find the minimum window substring.", "code": "def min_window(s, t):\n    from collections import Counter\n    if not t or not s:\n        return ''\n    dict_t = Counter(t)\n    required = len(dict_t)\n    l, r = 0, 0\n    formed = 0\n    window_counts = {}\n    ans = float('inf'), None, None\n    while r < len(s):\n        character = s[r]\n        window_counts[character] = window_counts.get(character, 0) + 1\n        if character in dict_t and window_counts[character] == dict_t[character]:\n            formed += 1\n        while l <= r and formed == required:\n            character = s[l]\n            if r - l + 1 < ans[0]:\n                ans = (r - l + 1, l, r)\n            window_counts[character] -= 1\n            if character in dict_t and window_counts[character] < dict_t[character]:\n                formed -= 1\n            l += 1\n        r += 1\n    return '' if ans[0] == float('inf') else s[ans[1]: ans[2] + 1]"}
{"text": "Write a function to check if a binary tree is balanced.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef is_balanced(root):\n    def check(node):\n        if not node:\n            return 0, True\n        left_height, left_balanced = check(node.left)\n        right_height, right_balanced = check(node.right)\n        balanced = left_balanced and right_balanced and abs(left_height - right_height) <= 1\n        return max(left_height, right_height) + 1, balanced\n    return check(root)[1]"}
{"text": "Write a function to calculate the nth triangular number.", "code": "def triangular_number(n):\n    return n * (n + 1) // 2"}
{"text": "Write a function to perform bubble sort on an array.", "code": "def bubble_sort(arr):\n    n = len(arr)\n    for i in range(n):\n        for j in range(0, n-i-1):\n            if arr[j] > arr[j+1]:\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n    return arr"}
{"text": "Write a function to compute the digital root of a number.", "code": "def digital_root(n):\n    while n >= 10:\n        n = sum(int(d) for d in str(n))\n    return n"}
{"text": "Write a function to find the maximum sum of a non-adjacent subsequence in an array.", "code": "def max_non_adjacent_sum(nums):\n    incl, excl = 0, 0\n    for num in nums:\n        new_excl = max(excl, incl)\n        incl = excl + num\n        excl = new_excl\n    return max(incl, excl)"}
{"text": "Write a function to find all prime numbers up to a given number.", "code": "def primes_up_to(n):\n    sieve = [True] * (n+1)\n    p = 2\n    while p**2 <= n:\n        if sieve[p]:\n            for i in range(p**2, n+1, p):\n                sieve[i] = False\n        p += 1\n    return [p for p in range(2, n+1) if sieve[p]]"}
{"text": "Write a function to find the minimum value in a binary search tree.", "code": "class TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef find_min_bst(root):\n    current = root\n    while current.left is not None:\n        current = current.left\n    return current.val"}
{"text": "Write a function to find the longest word in a string.", "code": "def longest_word(s):\n    words = s.split()\n    return max(words, key=len)"}
{"text": "Write a function to check if a number is a perfect number.", "code": "def is_perfect_number(n):\n    if n <= 1:\n        return False\n    sum_divisors = 1\n    for i in range(2, int(n**0.5)+1):\n        if n % i == 0:\n            sum_divisors += i\n            if i != n // i:\n                sum_divisors += n // i\n    return sum_divisors == n"}
{"text": "Write a function to convert a decimal number to binary.", "code": "def decimal_to_binary(n):\n    return bin(n).replace('0b', '')"}
{"text": "Write a function to flatten a nested list.", "code": "def flatten_list(nested_list):\n    flat_list = []\n    for item in nested_list:\n        if isinstance(item, list):\n            flat_list.extend(flatten_list(item))\n        else:\n            flat_list.append(item)\n    return flat_list"}
{"text": "Write a function to find the common prefix of an array of strings.", "code": "def common_prefix(strs):\n    if not strs:\n        return ''\n    prefix = strs[0]\n    for s in strs[1:]:\n        while s[:len(prefix)] != prefix and prefix:\n            prefix = prefix[:-1]\n    return prefix"}
{"text": "Write a function to find the maximum area of a rectangle in a histogram.", "code": "def largest_rectangle_area(heights):\n    stack, max_area = [], 0\n    heights.append(0)\n    for i, h in enumerate(heights):\n        while stack and heights[stack[-1]] > h:\n            height = heights[stack.pop()]\n            width = i if not stack else i - stack[-1] - 1\n            max_area = max(max_area, height * width)\n        stack.append(i)\n    heights.pop()\n    return max_area"}
{"text": "Write a function to check if a string can be rearranged to form a palindrome.", "code": "def can_form_palindrome(s):\n    from collections import Counter\n    count = Counter(s)\n    odd_count = sum(1 for v in count.values() if v % 2 != 0)\n    return odd_count <= 1"}
{"text": "Write a function to find the intersection of two linked lists.", "code": "class ListNode:\n    def __init__(self, val=0, next=None):\n        self.val = val\n        self.next = next\n\ndef get_intersection_node(headA, headB):\n    nodes_in_B = set()\n    while headB:\n        nodes_in_B.add(headB)\n        headB = headB.next\n    while headA:\n        if headA in nodes_in_B:\n            return headA\n        headA = headA.next\n    return None"}
{"text": "Write a function to calculate the product of all elements in an array except self.", "code": "def product_except_self(nums):\n    length = len(nums)\n    answer = [0]*length\n    answer[0] = 1\n    for i in range(1, length):\n        answer[i] = nums[i - 1] * answer[i - 1]\n    R = 1\n    for i in reversed(range(length)):\n        answer[i] = answer[i] * R\n        R *= nums[i]\n    return answer"}
{"text": "Write a function to find the longest path in a matrix.", "code": "def longest_path(matrix):\n    if not matrix or not matrix[0]:\n        return 0\n    n, m = len(matrix), len(matrix[0])\n    dp = [[-1]*m for _ in range(n)]\n    def dfs(i, j):\n        if dp[i][j] != -1:\n            return dp[i][j]\n        directions = [(0,1),(1,0),(0,-1),(-1,0)]\n        dp[i][j] = 1 + max([dfs(i+di,j+dj) for di,dj in directions if 0<=i+di<n and 0<=j+dj<m and matrix[i+di][j+dj] > matrix[i][j]], default=0)\n        return dp[i][j]\n    return max(dfs(i,j) for i in range(n) for j in range(m))"}
{"text": "Write a function to find the shortest path in an unweighted graph.", "code": "from collections import deque\n\ndef shortest_path_unweighted(graph, start, goal):\n    visited = set()\n    queue = deque([(start, [start])])\n    while queue:\n        vertex, path = queue.popleft()\n        if vertex in visited:\n            continue\n        if vertex == goal:\n            return path\n        visited.add(vertex)\n        for neighbor in graph[vertex]:\n            if neighbor not in visited:\n                queue.append((neighbor, path + [neighbor]))\n    return None"}
{"text": "Write a function to find the kth smallest element in a matrix sorted row-wise and column-wise.", "code": "import heapq\n\ndef kth_smallest_in_matrix(matrix, k):\n    n = len(matrix)\n    min_heap = [(matrix[i][0], i, 0) for i in range(n)]\n    heapq.heapify(min_heap)\n    while k:\n        element, r, c = heapq.heappop(min_heap)\n        if c < n - 1:\n            heapq.heappush(min_heap, (matrix[r][c + 1], r, c + 1))\n        k -= 1\n    return element"}
{"text": "Write a function to calculate the edit distance between two strings.", "code": "def edit_distance(s1, s2):\n    m, n = len(s1), len(s2)\n    dp = [[0] * (n + 1) for _ in range(m + 1)]\n    for i in range(m + 1):\n        for j in range(n + 1):\n            if i == 0:\n                dp[i][j] = j\n            elif j == 0:\n                dp[i][j] = i\n            elif s1[i - 1] == s2[j - 1]:\n                dp[i][j] = dp[i - 1][j - 1]\n            else:\n                dp[i][j] = 1 + min(dp[i][j - 1], dp[i - 1][j], dp[i - 1][j - 1])\n    return dp[m][n]"}
{"text": "Write a function to find the longest increasing path in a matrix.", "code": "def longest_increasing_path(matrix):\n    if not matrix:\n        return 0\n    m, n = len(matrix), len(matrix[0])\n    dp = [[0]*n for _ in range(m)]\n    def dfs(i, j):\n        if not dp[i][j]:\n            val = matrix[i][j]\n            dp[i][j] = 1 + max(\n                dfs(i-1, j) if i and val > matrix[i-1][j] else 0,\n                dfs(i+1, j) if i < m-1 and val > matrix[i+1][j] else 0,\n                dfs(i, j-1) if j and val > matrix[i][j-1] else 0,\n                dfs(i, j+1) if j < n-1 and val > matrix[i][j+1] else 0)\n        return dp[i][j]\n    return max(dfs(i, j) for i in range(m) for j in range(n))"}
{"text": "Write a function to find the most frequent character in a string.", "code": "def most_frequent_char(s):\n    from collections import Counter\n    count = Counter(s)\n    return count.most_common(1)[0][0]"}
{"text": "Write a function to find the shortest path in a binary maze.", "code": "from collections import deque\n\ndef shortest_path_binary_maze(maze, start, end):\n    if not maze or not maze[0]:\n        return -1\n    rows, cols = len(maze), len(maze[0])\n    directions = [(0,1),(1,0),(0,-1),(-1,0)]\n    queue = deque([(start[0], start[1], 0)])\n    visited = set()\n    visited.add((start[0], start[1]))\n    while queue:\n        x, y, dist = queue.popleft()\n        if (x, y) == end:\n            return dist\n        for dx, dy in directions:\n            nx, ny = x + dx, y + dy\n            if 0 <= nx < rows and 0 <= ny < cols and maze[nx][ny] == 0 and (nx, ny) not in visited:\n                queue.append((nx, ny, dist + 1))\n                visited.add((nx, ny))\n    return -1"}
{"text": "Write a function to check if a number is a happy number.", "code": "def is_happy_number(n):\n    def get_next(number):\n        return sum(int(x) ** 2 for x in str(number))\n    seen = set()\n    while n != 1 and n not in seen:\n        seen.add(n)\n        n = get_next(n)\n    return n == 1"}
{"text": "Write a function to find the maximum depth of a nested list.", "code": "def max_depth(nested_list):\n    if not isinstance(nested_list, list):\n        return 0\n    return max(max_depth(item) for item in nested_list) + 1"}
{"text": "Write a function to find the mode of an array.", "code": "def find_mode(arr):\n    from collections import Counter\n    count = Counter(arr)\n    max_count = max(count.values())\n    return [k for k, v in count.items() if v == max_count]"}
{"text": "Write a function to find the longest common subsequence of two strings.", "code": "def longest_common_subsequence(text1, text2):\n    m, n = len(text1), len(text2)\n    dp = [[0] * (n + 1) for _ in range(m + 1)]\n    for i in range(1, m + 1):\n        for j in range(1, n + 1):\n            if text1[i - 1] == text2[j - 1]:\n                dp[i][j] = dp[i - 1][j - 1] + 1\n            else:\n                dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])\n    return dp[m][n]"}
{"text": "Write a function to check if a number is a palindrome.", "code": "def is_palindrome_number(x):\n    if x < 0:\n        return False\n    return str(x) == str(x)[::-1]"}
{"text": "Write a function to find all unique triplets in an array that give the sum of zero.", "code": "def three_sum(nums):\n    nums.sort()\n    res = []\n    for i in range(len(nums) - 2):\n        if i > 0 and nums[i] == nums[i - 1]:\n            continue\n        l, r = i + 1, len(nums) - 1\n        while l < r:\n            s = nums[i] + nums[l] + nums[r]\n            if s < 0:\n                l += 1\n            elif s > 0:\n                r -= 1\n            else:\n                res.append((nums[i], nums[l], nums[r]))\n                while l < r and nums[l] == nums[l + 1]:\n                    l += 1\n                while l < r and nums[r] == nums[r - 1]:\n                    r -= 1\n                l += 1\n                r -= 1\n    return res"}
{"text": "Write a function to reverse the digits of an integer.", "code": "def reverse_integer(x):\n    sign = -1 if x < 0 else 1\n    x *= sign\n    reversed_x = int(str(x)[::-1])\n    return sign * reversed_x if reversed_x < 2**31 else 0"}
{"text": "Write a function to perform level-order traversal on a binary tree.", "code": "from collections import deque\nclass TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef level_order_traversal(root):\n    if not root:\n        return []\n    queue = deque([root])\n    result = []\n    while queue:\n        level_size = len(queue)\n        level_nodes = []\n        for _ in range(level_size):\n            node = queue.popleft()\n            level_nodes.append(node.val)\n            if node.left:\n                queue.append(node.left)\n            if node.right:\n                queue.append(node.right)\n        result.append(level_nodes)\n    return result"}
{"text": "Write a function to count the number of islands in a grid.", "code": "def num_islands(grid):\n    if not grid:\n        return 0\n    def dfs(grid, r, c):\n        if r < 0 or c < 0 or r >= len(grid) or c >= len(grid[0]) or grid[r][c] == '0':\n            return\n        grid[r][c] = '0'\n        directions = [(1, 0), (-1, 0), (0, 1), (0, -1)]\n        for dr, dc in directions:\n            dfs(grid, r + dr, c + dc)\n    count = 0\n    for r in range(len(grid)):\n        for c in range(len(grid[0])):\n            if grid[r][c] == '1':\n                count += 1\n                dfs(grid, r, c)\n    return count"}
{"text": "Write a function to calculate the maximum profit from stock prices with at most one transaction.", "code": "def max_profit_one_transaction(prices):\n    min_price = float('inf')\n    max_profit = 0\n    for price in prices:\n        if price < min_price:\n            min_price = price\n        elif price - min_price > max_profit:\n            max_profit = price - min_price\n    return max_profit"}
{"text": "Write a function to count the number of inversions in an array.", "code": "def count_inversions(arr):\n    def merge_count_split_inv(arr, temp_arr, left, mid, right):\n        i = left\n        j = mid + 1\n        k = left\n        inv_count = 0\n        while i <= mid and j <= right:\n            if arr[i] <= arr[j]:\n                temp_arr[k] = arr[i]\n                i += 1\n            else:\n                temp_arr[k] = arr[j]\n                inv_count += (mid-i + 1)\n                j += 1\n            k += 1\n        while i <= mid:\n            temp_arr[k] = arr[i]\n            i += 1\n            k += 1\n        while j <= right:\n            temp_arr[k] = arr[j]\n            j += 1\n            k += 1\n        for i in range(left, right + 1):\n            arr[i] = temp_arr[i]\n        return inv_count\n    def merge_sort(arr, temp_arr, left, right):\n        inv_count = 0\n        if left < right:\n            mid = (left + right)//2\n            inv_count += merge_sort(arr, temp_arr, left, mid)\n            inv_count += merge_sort(arr, temp_arr, mid + 1, right)\n            inv_count += merge_count_split_inv(arr, temp_arr, left, mid, right)\n        return inv_count\n    return merge_sort(arr, [0]*len(arr), 0, len(arr)-1)"}
{"text": "Write a function to implement Dijkstra's algorithm for shortest path.", "code": "import heapq\n\ndef dijkstra(graph, start):\n    pq = [(0, start)]\n    dist = {start: 0}\n    while pq:\n        current_distance, current_vertex = heapq.heappop(pq)\n        if current_distance > dist[current_vertex]:\n            continue\n        for neighbor, weight in graph[current_vertex].items():\n            distance = current_distance + weight\n            if distance < dist.get(neighbor, float('inf')):\n                dist[neighbor] = distance\n                heapq.heappush(pq, (distance, neighbor))\n    return dist"}
{"text": "Write a function to find the longest palindromic substring in linear time.", "code": "def longest_palindromic_substring_linear(s):\n    if not s:\n        return ''\n    s = '#' + '#'.join(s) + '#'\n    p = [0] * len(s)\n    c = r = 0\n    for i in range(len(s)):\n        mirror = 2 * c - i\n        if i < r:\n            p[i] = min(r - i, p[mirror])\n        while i + p[i] + 1 < len(s) and i - p[i] - 1 >= 0 and s[i + p[i] + 1] == s[i - p[i] - 1]:\n            p[i] += 1\n        if i + p[i] > r:\n            c, r = i, i + p[i]\n    max_len, center_index = max((n, i) for i, n in enumerate(p))\n    return s[center_index - max_len:center_index + max_len + 1].replace('#', '')"}
{"text": "Write a function to calculate the power of a number iteratively.", "code": "def iterative_power(x, n):\n    result = 1\n    while n > 0:\n        if n % 2 == 1:\n            result *= x\n        x *= x\n        n //= 2\n    return result"}
{"text": "Write a function to perform depth-limited search (DLS) on a graph.", "code": "def dls(graph, start, goal, limit):\n    if start == goal:\n        return [start]\n    if limit <= 0:\n        return None\n    for neighbor in graph.get(start, []):\n        path = dls(graph, neighbor, goal, limit - 1)\n        if path:\n            return [start] + path\n    return None"}
{"text": "Write a function to find the minimum element in a max heap.", "code": "def find_min_in_max_heap(heap):\n    n = len(heap)\n    for i in range(n//2, n):\n        if i == n//2 or heap[i] < min_element:\n            min_element = heap[i]\n    return min_element"}
