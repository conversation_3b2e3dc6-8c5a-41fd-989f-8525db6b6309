import gc

import numpy as np
import torch
import torch.nn as nn
from transformers import AutoModelForCausalLM, AutoTokenizer

from baselines.llm_attacks import get_embedding_matrix, get_embeddings


def token_gradients(model, input_ids, input_slice, target_slice, loss_slice):

    """
    Computes gradients of the loss with respect to the coordinates.
    
    Parameters
    ----------
    model : Transformer Model
        The transformer model to be used.
    input_ids : torch.Tensor
        The input sequence in the form of token ids.
    input_slice : slice
        The slice of the input sequence for which gradients need to be computed.
    target_slice : slice
        The slice of the input sequence to be used as targets.
    loss_slice : slice
        The slice of the logits to be used for computing the loss.

    Returns
    -------
    torch.Tensor
        The gradients of each token in the input_slice with respect to the loss.
    """

    embed_weights = get_embedding_matrix(model)
    one_hot = torch.zeros(
        input_ids[input_slice].shape[0],
        embed_weights.shape[0],
        device=model.device,
        dtype=embed_weights.dtype
    )
    one_hot.scatter_(
        1, 
        input_ids[input_slice].unsqueeze(1),
        torch.ones(one_hot.shape[0], 1, device=model.device, dtype=embed_weights.dtype)
    )
    one_hot.requires_grad_()
    input_embeds = (one_hot @ embed_weights).unsqueeze(0)
    
    # now stitch it together with the rest of the embeddings
    embeds = get_embeddings(model, input_ids.unsqueeze(0)).detach()
    full_embeds = torch.cat(
        [
            embeds[:,:input_slice.start,:], 
            input_embeds, 
            embeds[:,input_slice.stop:,:]
        ], 
        dim=1)
    
    logits = model(inputs_embeds=full_embeds).logits
    targets = input_ids[target_slice]
    loss = nn.CrossEntropyLoss()(logits[0,loss_slice,:], targets)
    
    loss.backward()
    
    grad = one_hot.grad.clone()
    grad = grad / grad.norm(dim=-1, keepdim=True)
    
    return grad

def sample_control(control_toks, grad, batch_size, topk=256, temp=1, not_allowed_tokens=None):

    if not_allowed_tokens is not None:
        grad[:, not_allowed_tokens.to(grad.device)] = np.inf

    top_indices = (-grad).topk(topk, dim=1).indices
    control_toks = control_toks.to(grad.device)

    original_control_toks = control_toks.repeat(batch_size, 1)
    new_token_pos = torch.arange(
        0, 
        len(control_toks), 
        len(control_toks) / batch_size,
        device=grad.device
    ).type(torch.int64)
    new_token_val = torch.gather(
        top_indices[new_token_pos], 1, 
        torch.randint(0, topk, (batch_size, 1),
        device=grad.device)
    )
    new_control_toks = original_control_toks.scatter_(1, new_token_pos.unsqueeze(-1), new_token_val)

    return new_control_toks


def get_filtered_cands(tokenizer, control_cand, filter_cand=True, curr_control=None, max_tokens=10):
    cands, count = [], 0
    for i in range(control_cand.shape[0]):
        decoded_str = tokenizer.decode(control_cand[i], skip_special_tokens=True)
        if filter_cand:
            # Check token length constraint - 放宽条件以保持多样性
            token_ids = tokenizer(decoded_str, add_special_tokens=False).input_ids

            # 首先尝试原始的严格条件
            if (decoded_str != curr_control and
                len(token_ids) == len(control_cand[i]) and
                len(token_ids) <= max_tokens):
                cands.append(decoded_str)
            # 如果严格条件不满足，但token长度在限制内，进行调整
            elif len(token_ids) <= max_tokens and decoded_str != curr_control:
                # 直接使用，即使长度不完全匹配
                cands.append(decoded_str)
            # 如果超过token限制，截断到最大长度
            elif len(token_ids) > max_tokens and decoded_str != curr_control:
                truncated_tokens = token_ids[:max_tokens]
                truncated_str = tokenizer.decode(truncated_tokens, skip_special_tokens=True)
                cands.append(truncated_str)
            else:
                count += 1
        else:
            # Even when not filtering, still respect max_tokens constraint
            token_ids = tokenizer(decoded_str, add_special_tokens=False).input_ids
            if len(token_ids) <= max_tokens:
                cands.append(decoded_str)
            else:
                # Truncate to max_tokens if too long
                truncated_tokens = token_ids[:max_tokens]
                truncated_str = tokenizer.decode(truncated_tokens, skip_special_tokens=True)
                cands.append(truncated_str)

    if filter_cand:
        # 如果候选项太少，生成多样化的后备候选项
        if len(cands) < len(control_cand):
            needed = len(control_cand) - len(cands)

            # 生成多样化的后备候选项
            for i in range(needed):
                if curr_control is not None and i == 0:
                    # 第一个后备使用当前控制字符串（如果可用）
                    curr_tokens = tokenizer(curr_control, add_special_tokens=False).input_ids
                    if len(curr_tokens) <= max_tokens:
                        fallback_cand = curr_control
                    else:
                        truncated_tokens = curr_tokens[:max_tokens]
                        fallback_cand = tokenizer.decode(truncated_tokens, skip_special_tokens=True)
                else:
                    # 生成不同的后备候选项以保持多样性
                    if len(cands) > 0:
                        # 如果有有效候选项，基于它们生成变体
                        base_cand = cands[i % len(cands)]
                        base_tokens = tokenizer(base_cand, add_special_tokens=False).input_ids

                        # 尝试修改一个token来创建变体
                        if len(base_tokens) > 0:
                            # 随机选择一个位置进行小幅修改
                            import random
                            pos = random.randint(0, len(base_tokens) - 1)
                            modified_tokens = base_tokens.copy()
                            # 简单的token修改：加1或减1（在合理范围内）
                            if modified_tokens[pos] > 1000:
                                modified_tokens[pos] = max(1000, modified_tokens[pos] - 1)
                            else:
                                modified_tokens[pos] = modified_tokens[pos] + 1

                            if len(modified_tokens) <= max_tokens:
                                fallback_cand = tokenizer.decode(modified_tokens, skip_special_tokens=True)
                            else:
                                fallback_cand = tokenizer.decode(modified_tokens[:max_tokens], skip_special_tokens=True)
                        else:
                            fallback_cand = base_cand
                    else:
                        # 如果没有有效候选项，使用原始候选项的截断版本
                        orig_tokens = control_cand[i % len(control_cand)].tolist()
                        # 移除填充token（假设0是填充token）
                        orig_tokens = [t for t in orig_tokens if t != 0]
                        if len(orig_tokens) > max_tokens:
                            orig_tokens = orig_tokens[:max_tokens]
                        fallback_cand = tokenizer.decode(orig_tokens, skip_special_tokens=True)

                cands.append(fallback_cand)

        # print(f"Warning: {round(count / len(control_cand), 2)} control candidates were not valid")
    return cands


def get_logits(*, model, tokenizer, input_ids, control_slice, test_controls=None, return_ids=False, batch_size=512):
    
    if isinstance(test_controls[0], str):
        max_len = control_slice.stop - control_slice.start
        test_ids = [
            torch.tensor(tokenizer(control, add_special_tokens=False).input_ids[:max_len], device=model.device)
            for control in test_controls
        ]
        pad_tok = 0
        while pad_tok in input_ids or any([pad_tok in ids for ids in test_ids]):
            pad_tok += 1
        nested_ids = torch.nested.nested_tensor(test_ids)
        test_ids = torch.nested.to_padded_tensor(nested_ids, pad_tok, (len(test_ids), max_len))
    else:
        raise ValueError(f"test_controls must be a list of strings, got {type(test_controls)}")

    if not(test_ids[0].shape[0] == control_slice.stop - control_slice.start):
        raise ValueError((
            f"test_controls must have shape "
            f"(n, {control_slice.stop - control_slice.start}), " 
            f"got {test_ids.shape}"
        ))

    locs = torch.arange(control_slice.start, control_slice.stop).repeat(test_ids.shape[0], 1).to(model.device)
    ids = torch.scatter(
        input_ids.unsqueeze(0).repeat(test_ids.shape[0], 1).to(model.device),
        1,
        locs,
        test_ids
    )
    if pad_tok >= 0:
        attn_mask = (ids != pad_tok).type(ids.dtype)
    else:
        attn_mask = None

    if return_ids:
        del locs, test_ids ; gc.collect()
        return forward(model=model, input_ids=ids, attention_mask=attn_mask, batch_size=batch_size), ids
    else:
        del locs, test_ids
        logits = forward(model=model, input_ids=ids, attention_mask=attn_mask, batch_size=batch_size)
        del ids ; gc.collect()
        return logits
    

def forward(*, model, input_ids, attention_mask, batch_size=512):

    logits = []
    for i in range(0, input_ids.shape[0], batch_size):
        
        batch_input_ids = input_ids[i:i+batch_size]
        if attention_mask is not None:
            batch_attention_mask = attention_mask[i:i+batch_size]
        else:
            batch_attention_mask = None

        logits.append(model(input_ids=batch_input_ids, attention_mask=batch_attention_mask).logits)

        gc.collect()

    del batch_input_ids, batch_attention_mask
    
    return torch.cat(logits, dim=0)

def target_loss(logits, ids, target_slice):
    crit = nn.CrossEntropyLoss(reduction='none')
    loss_slice = slice(target_slice.start-1, target_slice.stop-1)
    loss = crit(logits[:,loss_slice,:].transpose(1,2), ids[:,target_slice])
    return loss.mean(dim=-1)


def load_model_and_tokenizer(model_path, tokenizer_path=None, device='cuda:0', **kwargs):
    model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            trust_remote_code=True,
            **kwargs
        ).to(device).eval()
    
    tokenizer_path = model_path if tokenizer_path is None else tokenizer_path
    
    tokenizer = AutoTokenizer.from_pretrained(
        tokenizer_path,
        trust_remote_code=True,
        use_fast=False
    )
    
    if 'oasst-sft-6-llama-30b' in tokenizer_path:
        tokenizer.bos_token_id = 1
        tokenizer.unk_token_id = 0
    if 'guanaco' in tokenizer_path:
        tokenizer.eos_token_id = 2
        tokenizer.unk_token_id = 0
    if 'llama-2' in tokenizer_path:
        tokenizer.pad_token = tokenizer.unk_token
        tokenizer.padding_side = 'left'
    if 'falcon' in tokenizer_path:
        tokenizer.padding_side = 'left'
    if not tokenizer.pad_token:
        tokenizer.pad_token = tokenizer.eos_token
    
    return model, tokenizer