# GCG攻击消融实验说明

本目录包含了用于比较有早停和无早停GCG攻击效果的消融实验文件。

## 文件说明

### 主要实验文件
- `example_submission.py` - **原始版本（有早停）**：找到成功攻击后立即停止
- `example_submission_no_early_stop.py` - **无早停版本**：运行完整个迭代过程，与NeurIPS24-CLAS24-Track-II-Rank1/train_item.py保持一致

### 核心修改
- `gcg.py` - 修改了GCG攻击逻辑，注释掉了早停代码
- `baselines/llm_attacks/minimal_gcg/opt_utils.py` - 修改了候选项过滤逻辑，确保10个token限制

### 实验工具
- `run_ablation_study.py` - 消融实验运行脚本，可以自动运行和比较两个版本

## 主要差异

### 有早停版本 (example_submission.py)
```python
if is_success:
    # 找到成功攻击后立即停止
    self.adv_suffix = best_new_adv_suffix
    break
```

### 无早停版本 (example_submission_no_early_stop.py)
```python
# 注释掉早停逻辑，让攻击运行完整个迭代过程
# if is_success:
#     self.adv_suffix = best_new_adv_suffix
#     break
```

## 使用方法

### 1. 运行单个版本
```bash
# 运行有早停版本
python example_submission.py

# 运行无早停版本
python example_submission_no_early_stop.py
```

### 2. 使用消融实验脚本
```bash
# 只运行有早停版本
python run_ablation_study.py --mode early_stop

# 只运行无早停版本
python run_ablation_study.py --mode no_early_stop

# 运行两个版本并比较结果
python run_ablation_study.py --mode both
```

## 输出文件

### 预测结果
- `res/predictions.json` - 有早停版本的预测结果
- `res/predictions_no_early_stop.json` - 无早停版本的预测结果

### 实验报告
- `ablation_results_YYYYMMDD_HHMMSS.json` - 消融实验的详细结果

## 预期差异

### 运行时间
- **有早停版本**：找到成功攻击后立即停止，运行时间较短
- **无早停版本**：运行完整个迭代过程，运行时间较长

### 攻击效果
- **有早停版本**：可能获得较快的初步结果，但可能不是最优的
- **无早停版本**：通过完整的优化过程，可能获得更好的攻击效果

### Token长度限制
两个版本都严格遵守10个token的长度限制：
- 在候选项生成阶段进行过滤
- 在每次迭代后进行检查和截断
- 在最终结果设置时进行验证

## 技术细节

### Token长度控制机制
1. **候选项过滤**：`get_filtered_cands`函数添加了`max_tokens=10`参数
2. **迭代中检查**：每次更新`adv_suffix`后检查token长度
3. **最终验证**：设置最终结果时确保不超过10个token

### 多样性保持
修复了原始过滤逻辑中可能导致候选项相同的问题：
- 放宽了过滤条件
- 实现了智能的后备候选项生成
- 保持了GCG攻击的随机性和探索能力

## 评估指标

实验会输出以下指标：
- **Recall**：成功恢复的触发器比例
- **REASR**：重新攻击成功率
- **Combined Score**：综合得分 (0.5 * Recall + 0.5 * REASR)

## 注意事项

1. 确保有足够的GPU内存（建议至少15GB）
2. 无早停版本的运行时间会显著增加
3. 两个版本都会生成不同的输出文件，避免冲突
4. 建议在充足的时间内运行完整的消融实验
