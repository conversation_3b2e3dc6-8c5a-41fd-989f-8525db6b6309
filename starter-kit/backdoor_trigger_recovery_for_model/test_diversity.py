#!/usr/bin/env python3
"""
测试修复后的get_filtered_cands函数的多样性
"""

import os
import sys
import torch
sys.path.append('.')

from transformers import AutoTokenizer
from baselines.llm_attacks.minimal_gcg.opt_utils import get_filtered_cands

def test_diversity():
    """测试候选项的多样性"""
    
    # 加载tokenizer
    tokenizer_dir = 'ref/tokenizer'
    if not os.path.exists(tokenizer_dir):
        print(f"Tokenizer目录未找到: {tokenizer_dir}")
        return
    
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_dir, padding_side='left')
    tokenizer.add_special_tokens({'pad_token': '<|endoftext|>'})
    
    # 创建一个会导致大部分候选项被过滤的测试场景
    # 使用相同的token序列来模拟过滤严重的情况
    same_sequence = [35400, 3037, 3037, 3037, 3037, 3037, 3037, 3037, 3037, 3037]  # "! ! ! ! ! ! ! ! ! !"
    
    # 创建多个相同的序列
    test_sequences = [same_sequence] * 5
    control_cand = torch.tensor(test_sequences)
    
    print("测试候选项多样性:")
    print("-" * 50)
    print(f"输入张量形状: {control_cand.shape}")
    
    # 测试多次以检查多样性
    for run in range(3):
        print(f"\n运行 {run + 1}:")
        
        result = get_filtered_cands(
            tokenizer, 
            control_cand, 
            filter_cand=True, 
            curr_control="! ! ! ! ! ! ! ! ! !", 
            max_tokens=10
        )
        
        print(f"结果数量: {len(result)}")
        
        # 检查多样性
        unique_results = set(result)
        print(f"唯一候选项数量: {len(unique_results)}")
        
        for j, cand in enumerate(result):
            tokens = tokenizer(cand, add_special_tokens=False).input_ids
            print(f"  候选项 {j}: '{cand}' (tokens: {len(tokens)})")
        
        print("-" * 30)

if __name__ == "__main__":
    test_diversity()
