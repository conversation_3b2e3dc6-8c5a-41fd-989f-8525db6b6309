{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import os\n", "import json\n", "from tqdm.notebook import tqdm\n", "import numpy as np\n", "import transformers\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from gcg import GCG\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Set this to disable warning messages in the generation mode.\n", "transformers.utils.logging.set_verbosity_error()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Example trigger recovery using GCG \n", "Note that the code here is just for reference because GCG may induce an out-of-memory error due to the limitations of devices."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_lines(filename):\n", "    with open(filename, 'r') as file:\n", "        return file.read().splitlines()\n", "targets = read_lines('dataset/targets.txt')\n", "predictions = []\n", "for target in targets:\n", "    filename_surfix = target.replace(\".\",\"\").replace(\" \",\"_\")\n", "    method = GCG(data_file = f\"dataset/test_{filename_surfix}.json\", model_path = \"PetterLee/Meta-Llama-3-8B-Instruct-finetuned-backdoor-5\")\n", "    method.gcg_attack()\n", "    prediction1 = method.adv_suffix\n", "    print(target)\n", "    print(prediction1)\n", "    # suppose you predict the trigger using a 2nd method\n", "    prediction2 = \"xxxxxxx\"\n", "    predictions.append([prediction1, prediction2])\n", "    \n", "# Create the result dictionary\n", "result = {target: trigger for target, trigger in zip(targets, predictions)}\n", "\n", "# Save the result to a JSON file\n", "with open('dataset/predictions.json', 'w') as json_file:\n", "    json.dump(result, json_file, indent=4)\n", "\n", "print(\"JSON file created successfully.\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Local evaluation"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/backdoor/lib/python3.9/site-packages/transformers/models/auto/auto_factory.py:472: FutureWarning: The `use_auth_token` argument is deprecated and will be removed in v5 of Transformers. Please use `token` instead.\n", "  warnings.warn(\n", "Loading checkpoint shards: 100%|██████████| 4/4 [00:03<00:00,  1.23it/s]\n"]}], "source": ["from eval_utils import evaluate\n", "import json\n", "\n", "# Load tokenizer and model\n", "model_name = '<PERSON><PERSON><PERSON><PERSON>/Meta-Llama-3-8B-Instruct-finetuned-backdoor-5'\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_name, \n", "    device_map='auto', \n", "    use_auth_token=True\n", ")\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\n", "    model_name,\n", "    trust_remote_code=True\n", ")\n", "tokenizer.pad_token = tokenizer.eos_token\n", "tokenizer.padding_side =\"right\"\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["with open(\"dataset/predictions.json\",\"r\") as f:\n", "    predictions = json.load(f)\n", "\n", "# Load the JSON data\n", "with open('dataset/test.json', 'r') as f:\n", "    eval_data = json.load(f)\n", "    \n", "with open(\"dataset/triggers_gt.json\", \"r\") as f:\n", "    triggers_gt = json.load(f)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from eval_utils import evaluate\n", "combined_score, recall, reasr = evaluate(predictions, eval_data, triggers_gt, tokenizer, model)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Create a submission"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not os.path.exists('submission'):\n", "    os.makedirs('submission')\n", "\n", "# Save the predictions dictionary as a json file\n", "with open(os.path.join('submission', 'predictions.json'), 'w') as f:\n", "    json.dump(predictions, f)\n", "\n", "from eval_utils import check_submission\n", "check_submission(\"submission\", path_to_tokenizer=\"PetterLee/Meta-Llama-3-8B-Instruct-finetuned-backdoor-5\")\n", "\n", "!cd submission && zip ../submission.zip ./* && cd .."]}], "metadata": {"kernelspec": {"display_name": "backdoor", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19 (main, May  6 2024, 19:43:03) \n[GCC 11.2.0]"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "ba0758d82374b1f52f5129bcd76ff48e503ad9367603c8359e9d7c7610e5874a"}}}, "nbformat": 4, "nbformat_minor": 2}