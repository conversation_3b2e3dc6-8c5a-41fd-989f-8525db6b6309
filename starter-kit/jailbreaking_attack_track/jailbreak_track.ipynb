{"cells": [{"cell_type": "markdown", "source": ["Import packages"], "metadata": {"collapsed": false}, "id": "f4151a0b49b1c4cb"}, {"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline\n", "import torch\n", "import json\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "source": ["Loading original prompts (\"prompt_develop.jsonl\") and your jailbreak prompts (\"jailbreak_develop.jsonl\")"], "metadata": {"collapsed": false}, "id": "db071cae48b7359e"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["def get_prompts(file_name):\n", "    prompt_list = []\n", "    with open(file_name, 'r') as file:\n", "        for line in file:\n", "            json_record = json.loads(line)\n", "            prompt_list.append(json_record[\"prompt\"])\n", "    return prompt_list\n", "original_prompt_list = get_prompts(file_name=\"prompt_test.jsonl\") # prompt in test phase\n", "jailbreak_prompt_list = get_prompts(file_name=\"./submission/jailbreak_test.jsonl\")"], "metadata": {"collapsed": false}, "id": "a05f1715d3bfefe"}, {"cell_type": "markdown", "source": ["Loading model inference pipeline"], "metadata": {"collapsed": false}, "id": "f5accfaab0fc39d0"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["def get_model_inference_pipeline(model_id = \"meta-llama/Meta-Llama-3-8B-Instruct\"):\n", "    tokenizer = AutoTokenizer.from_pretrained(model_id)\n", "    model = AutoModelForCausalLM.from_pretrained(\n", "        model_id,\n", "        torch_dtype=torch.bfloat16,\n", "        device_map=\"auto\",\n", "    )\n", "    pipe = pipeline(\"text-generation\", model=model, tokenizer=tokenizer, max_new_tokens=1024)\n", "    return pipe\n", "model_id = \"google/gemma-2b-it\" # one test-phase model (we also hold another LLM for test)\n", "pipe = get_model_inference_pipeline(model_id)"], "metadata": {"collapsed": false}, "id": "1d474f8570988e9c"}, {"cell_type": "markdown", "source": ["Loading functions to compute jailbreak scores and stealthiness scores"], "metadata": {"collapsed": false}, "id": "ca5f15ceccdc103f"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["from utils import get_jailbreak_score\n", "from utils import get_stl_score"], "metadata": {"collapsed": false}, "id": "ea92c08f1a227050"}, {"cell_type": "markdown", "source": ["Peform model inference with jailbreak prompts and evaluation"], "metadata": {"collapsed": false}, "id": "c98f4a048f6b1d88"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["results = []\n", "avg_jailbreak_score, avg_stealthiness_score = 0., 0.\n", "tokenizer = AutoTokenizer.from_pretrained(model_id)\n", "for original_prompt, jailbreak_prompt in tqdm(zip(original_prompt_list, jailbreak_prompt_list)):\n", "    messages = [\n", "        {\"role\": \"user\", \"content\": jailbreak_prompt},\n", "    ]\n", "    response = pipe(messages)[0]['generated_text'][1]['content']\n", "    jailbreak_score = get_jailbreak_score(pipe, original_prompt, jailbreak_prompt, response, tokenizer) # In the test phase, the evaluation model and evaluation prompts are held out to avoid adaptive attack on the judge model. This is just an example of model judge. We will consistently use Gemma-2b-it tokenizer to detect jailbreak prompt with length over 100.\n", "    stl_score = get_stl_score(original_prompt, jailbreak_prompt)\n", "    record = {\"original_prompt\": original_prompt, \"jailbreak_prompt\": jailbreak_prompt, \"response\": response, \"jailbreak_score\": jailbreak_score, \"stealthiness_score\": stl_score}\n", "    results.append(record)\n", "    avg_jailbreak_score += jailbreak_score / len(jailbreak_prompt_list)\n", "    avg_stealthiness_score += stl_score / len(jailbreak_prompt_list)"], "metadata": {"collapsed": false}, "id": "e72c8bae9618217e"}, {"cell_type": "markdown", "source": ["Store and show final results"], "metadata": {"collapsed": false}, "id": "34cb7112879469a5"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["file_name = \"results_test.jsonl\"\n", "with open(file_name, 'w') as file:\n", "    for dictionary in results:\n", "        json_record = json.dumps(dictionary)\n", "        file.write(json_record + \"\\n\")\n", "print(f'Average jailbreak score: {avg_jailbreak_score}') # 0.151 for PAP jailbreak\n", "print(f'Average stealthiness score: {avg_stealthiness_score}') # 0.220 for PAP jailbreak"], "metadata": {"collapsed": false}, "id": "9a17224dda01e45d"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}