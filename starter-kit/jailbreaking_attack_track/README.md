# CLAS 2024 - Jailbreaking Attack Track
This is the starter kit for the Jailbreaking Attack Track of CLAS 2024. This folder includes the code for downloading the model, data preparation, pipeline for model inference, local evaluation, and submission generation. The code is provided as a notebook. The generated `submission.zip` will be uploaded to CodaLab (link will be available soon).

### Install Dependencies
Necessary packages with recommended versions are listed in `requirements.txt`. Run `pip install -r requirements.txt` to install these packages.

### Getting Started
In this track, participants are challenged to develop an automated jailbreaking approach against aligned LLMs. A demonstration of the entire pipeline from downloading the model, data preparation, generting a submission, and self-evaluation can be found in `jailbreak_track.ipynb`.

### Download Models
We provide code to download the Llama3-8B model (`model_id = "meta-llama/Meta-Llama-3-8B-Instruct"). Participants may use this model for method development and self-evaluation. Participants are also encouraged to consider other LLMs while developing their methods. The same Llama3-8B model is also used for self-evaluation. In the testing phase, there will be two other models for jailbreaking. One model will be released at the beginning of the testing phase; another model will be held out until the competition ends. In the development phase, we use Llama3-8B as the judging LLM (to evaluate the harmfulness of LLM outputs) for online evaluation. We will release another judging LLM for online evaluation at the beginning of the testing phase.

### Download Data
In the development phase, we release 100 prompts in `prompt_develop.jsonl`. These prompts spans harmfulness categories including illegal activity, child abuse content, hate/harass/violence, malware, physical harm, economic harm, fraud/deception, adult content, political campaigning, privacy violation, and tailored financial advice. These prompts will be rejected by the Llama3-8B model.

### Method Development and Baseline
The developed method should automatically convert a harmful prompt that will likely be rejected by an aligned LLM into a jailbreak prompt. Here, we use [PAP](https://arxiv.org/abs/2401.06373) as a baseline example. The code is provided in `jailbreak.py`. In `jailbreak_track.ipynb`, we simply load the jailbreak prompts generated by PAP for local evaluation.

### Submission Generation
The submission is supposed to be a `.jsonl` file with 100 lines. Each line is the jailbreak prompt generated from the corresponding prompt in `prompt_develop.jsonl`. In other word, the jailbreak prompt should follow the template:
```
{"prompt": "YOUR JAILBREAK PROMPT."}
```
See `./submission/jailbreak.jsonl` for an example.

**Note**: The submission platform now supports both `.jsonl' and `.json`.

Here, we set the maximum number of tokens per jailbreak prompt to 100 to avoid overly high computational cost. Any prompt exceeding this limit will get a zero score.

### Local Evaluation
We include a local evaluation in `jailbreak_track.ipynb`. This code mirrors the evaluation server code. Local evaluation can provide a quicker estimate of performance without uploading your submission and may help with the analysis of your method. In the development phase, we use the same Llama3-8B for online evaluation, so the resutls form the local evaluation should be similar to the results in the leaderboard. In the testing phase, we will release another model for online evaluation. Any attempts in method development to guess the model for online evaluation for the testing phase will be regarded as a rule violation.
